---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-config
  namespace: backend
data:
  host: uat-nw-db-mysql-nyc1-36593-do-user-13112112-0.m.db.ondigitalocean.com
  port: "25060"
  claimsDbName: claims
  membershipDbName: membership
  profileDbName: profile
  notificationDbName: notification
  ticketingDbName: ticketing
  keycloakDbName: keycloakDb
  stagingDbName: staging

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: backend
data:
  vitrayaBaseUrl: https://opd-dev.vitraya.com
  vitrayaUsername: lct.api.dev
  vitrayaPassword: vitraya123
  vitrayaSecretKey: cGBAugX5aDMHD4I85lHqbGKjXHDX8ruG
  vitrayaSaltKey: my_liasion_salt_lol
  keycloakClient: api-services
  keycloakSecret: poFy8D74BQpEhmn8vp2xW4vgtQgaZTnT
#  documentBaseUrl: http://documents-service:8095
  documentBaseUrl: https://api.lctafrica.net

---
apiVersion: v1
kind: Secret
metadata:
  name: mysql-secrets
  namespace: backend
data:
  username: ZG9hZG1pbg==
  password: QVZOU19ZMVVvSzRacW1VZEVCM2pLZkdN
type: Opaque

---
apiVersion: v1
kind: Secret
metadata:
  name: keycloak-secrets
  namespace: backend
data:
  username: ZG9hZG1pbg==
  password: QVZOU19ZMVVvSzRacW1VZEVCM2pLZkdN
type: Opaque
