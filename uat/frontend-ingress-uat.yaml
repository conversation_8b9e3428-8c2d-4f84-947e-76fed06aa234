---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-admin-portal
  namespace: frontend
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/limit-connections: "100"
    nginx.ingress.kubernetes.io/limit-rpm: "100"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"
spec:
  ingressClassName: nginx
  rules:
  - host: admin-uat.lctafrica.net
    http:
      paths:
      - backend:
          service:
            name: admin-portal-service
            port:
              number: 3000
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - admin-uat.lctafrica.net
    secretName: admin-uat-letsencrypt-prod

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-provider-portal
  namespace: frontend
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/limit-connections: "100"
    nginx.ingress.kubernetes.io/limit-rpm: "100"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"
spec:
  ingressClassName: nginx
  rules:
  - host: provider-uat.lctafrica.net
    http:
      paths:
      - backend:
          service:
            name: provider-portal-service
            port:
              number: 3000
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - provider-uat.lctafrica.net
    secretName: provider-uat-letsencrypt-prod

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-onboard-portal
  namespace: frontend
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/limit-connections: "100"
    nginx.ingress.kubernetes.io/limit-rpm: "100"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"
spec:
  ingressClassName: nginx
  rules:
  - host: onboard-uat.lctafrica.net
    http:
      paths:
      - backend:
          service:
            name: onboard-portal-service
            port:
              number: 3000
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - onboard-uat.lctafrica.net
    secretName: onboard-uat-letsencrypt-prod

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-payer-portal
  namespace: frontend
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/limit-connections: "100"
    nginx.ingress.kubernetes.io/limit-rpm: "100"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"
spec:
  ingressClassName: nginx
  rules:
  - host: payer-uat.lctafrica.net
    http:
      paths:
      - backend:
          service:
            name: payer-portal-service
            port:
              number: 3000
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - payer-uat.lctafrica.net
    secretName: payer-uat-letsencrypt-prod
