#################################################################################
####################### 1st Ingress Rule - rewrite ##############################
#################################################################################
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-claims
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"

spec:
  #   tls:
  #   - hosts:
  #     - "*.lctafrica.net"
  #     secretName: lct-tls-secret
  rules:
    - http:
        paths:
          - path: /api/v1/preauthorization/.*
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/document/.*
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/benefit/.*
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/visit/.*
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/migration/.*
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/claim/.*
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/payer-claim/.*
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088

  ingressClassName: nginx

---
#################################################################################
####################### 1st Ingress Rule - rewrite ##############################
#################################################################################

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-membership
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"

spec:
  #  tls:
  #    - hosts:
  #        - "*.lctafrica.net"
  #      secretName: lct-tls-secret
  rules:
    - http:
        paths:
          - path: /api/v1/membership/.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/provider.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/provider/.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/device/.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/country/.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/catalog/.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/audit/.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/card/.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/restriction.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/services.*
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070

  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-gateway
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
  # annotations:
  #   cert-manager.io/issuer: letsencrypt-nginx
spec:
  # tls:
  #   - hosts:
  #     - quote.starter-kit.online
  #     secretName: letsencrypt-nginx-quote
  rules:
    - http:
        paths:
          - path: /api/v1/search/.*
            pathType: Prefix
            backend:
              service:
                name: gateway-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-notification
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    nginx.ingress.kubernetes.io/proxy-ssl-verify: "off"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  #  tls:
  #    - hosts:
  #        - "*.lctafrica.net"
  #      secretName: lct-tls-secret
  rules:
    - http:
        paths:
          - path: /api/v1/notification/.*
            pathType: Prefix
            backend:
              service:
                name: notification-service
                port:
                  number: 8080

          - path: /api/v1/events/
            pathType: Prefix
            backend:
              service:
                name: notification-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-documents
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 100m
  # annotations:
  #   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "*.lctafrica.net"
  #      secretName: lct-tls-secret
  rules:
    #    - host: api-uat.lctafrica.net
    - http:
        paths:
          - path: /api/file/
            pathType: Prefix
            backend:
              service:
                name: documents-service
                port:
                  number: 8095

          - path: /api/britam/
            pathType: Prefix
            backend:
              service:
                name: documents-service
                port:
                  number: 8095
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-profile
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
  # annotations:
  #   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "*.lctafrica.net"
  #      secretName: lct-tls-secret
  rules:
    #    - host: api-uat.lctafrica.net
    - http:
        paths:
          - path: /api/v1/profile/.*
            pathType: Prefix
            backend:
              service:
                name: profile-service
                port:
                  number: 8080
          - path: /api/v1/biometric/.*
            pathType: Prefix
            backend:
              service:
                name: profile-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-staging
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
  # annotations:
  #   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "*.lctafrica.net"
  #      secretName: lct-tls-secret
  rules:
    #    - host: api-uat.lctafrica.net
    - http:
        paths:
          - path: /api/v1/staging/.*
            pathType: Prefix
            backend:
              service:
                name: staging-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-keycloak
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    kubernetes.io/ingress.class: nginx
    #    cert-manager:io/cluster-issuer: lets-encrypt-staging
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: ALLOWALL";
#      more_set_headers  "Content-Security-Policy "default-src *  data: blob: filesystem: about: ws: wss: 'unsafe-inline' 'unsafe-eval' 'unsafe-dynamic'; script-src * data: blob: 'unsafe-inline' 'unsafe-eval'; connect-src * data: blob: 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src * data: blob: ; style-src * data: blob: 'unsafe-inline'; font-src * data: blob: 'unsafe-inline';"
#      more_set_headers "X-Xss-Protection: 1; mode=block";
#      more_set_headers "X-Content-Type-Options: nosniff";
#      more_clear_headers "Cache-Control";
#      more_set_headers "Cache-Control: must-revalidate";
#      proxy_hide_header Content-Security-Policy;
#      proxy_hide_header "X-Frame-Options";
#      proxy_hide_header "X-Frame-Options";
#      proxy_set_header l5d-dst-override;

#    cert-manager.io/acme-challenge-type: http01
#    nginx.ingress.kubernetes.io/use-regex: "true"
#    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
#    kubernetes.io/ingress.class: nginx
#    cert-manager.io/issuer: letsencrypt-nginx
#  annotations:
#    kubernetes.io/ingress.class: nginx
#    cert-manager:io/cluster-issuer: nlets-encrypt-staging
spec:
  #  tls:
  #    - hosts:
  #        - "*.lctafrica.net"
  #      secretName: lct-tls-secret
  rules:
    - host: keycloak.lctafrica.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: keycloak-service-2
                port:
                  number: 8090
  ingressClassName: nginx

---
apiVersion: v1
kind: Service
metadata:
  name: keycloak-service-2
  namespace: default
  labels:
    app: keycloak
spec:
  ports:
    - name: http
      port: 8090
      targetPort: 8080
  selector:
    app: keycloak
  type: ClusterIP
  sessionAffinity: ClientIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-ussd
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
  # annotations:
  #   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "*.lctafrica.net"
  #      secretName: lct-tls-secret
  rules:
    #    - host: api-uat.lctafrica.net
    - http:
        paths:
          - path: /ussd/.*
            pathType: Prefix
            backend:
              service:
                name: ussd-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-reports
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
spec:
  # tls:
  #   - hosts:
  #     - quote.starter-kit.online
  #     secretName: letsencrypt-nginx-quote
  rules:
    - http:
        paths:
          - path: /api/v1/reports/.*
            pathType: Prefix
            backend:
              service:
                name: reports-service
                port:
                  number: 8096
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-reports-100000-seconds
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/limit-connections: "300"
    nginx.ingress.kubernetes.io/limit-rpm: "300"
    nginx.ingress.kubernetes.io/limit-status-code: "429"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "1000s"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1000s"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1000s"
    nginx.ingress.kubernetes.io/proxy-body-size: 900m
spec:
  # tls:
  #   - hosts:
  #     - quote.starter-kit.online
  #     secretName: letsencrypt-nginx-quote
  rules:
    - http:
        paths:
          - path: /api/v1/reports/claimsInRange/.*
            pathType: Prefix
            backend:
              service:
                name: reports-service
                port:
                  number: 8096
  ingressClassName: nginx
