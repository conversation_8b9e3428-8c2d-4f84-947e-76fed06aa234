---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-config
  namespace: backend
data:
  host: db-mysql-nyc1-73605-do-user-13112112-0.b.db.ondigitalocean.com
  port: "25060"
  claimsDbName: claims
  membershipDbName: membership
  profileDbName: profile
  notificationDbName: notification
  ticketingDbName: ticketing
  keycloakDbName: keycloak
  stagingDbName: staging

---

apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: backend
data:
  vitrayaBaseUrl: https://opd-dev.vitraya.com
  vitrayaUsername: lct.api.dev
  vitrayaPassword: vitraya123
  vitrayaSecretKey: cGBAugX5aDMHD4I85lHqbGKjXHDX8ruG
  vitrayaSaltKey: my_liasion_salt_lol
  keycloakClient: api-services
  keycloakSecret: poFy8D74BQpEhmn8vp2xW4vgtQgaZTnT

---
apiVersion: v1
kind: Secret
metadata:
  name: mysql-secrets
  namespace: backend
data:
  username: ZG9hZG1pbg==
  password: QVZOU182MEIwQ3p4NDB2ZkR2czZoMV9W
type: Opaque

---
apiVersion: v1
kind: Secret
metadata:
  name: keycloak-secrets
  namespace: backend
data:
  username: bGN0LWFkbWlu
  password: bGN0LWFkbWluLXNlY3JldA==
type: Opaque
