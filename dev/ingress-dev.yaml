#################################################################################
####################### 1st Ingress Rule - rewrite ##############################
#################################################################################
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-claims
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
#    cert-manager.io/cluster-issuer: letsencrypt-prod
#    nginx.ingress.kubernetes.io/configuration-snippet: |
#      add_header 'Access-Control-Allow-Origin' "*.lctafrica.net" always;
#      add_header 'Access-Control-Allow-Credentials' "true" always;
#      add_header 'Access-Control-Allow-Methods' "PUT, GET, POST, DELETE, OPTIONS" always;

#    nginx.ingress.kubernetes.io/cors-allow-origin: http://localhost:3002
# annotations:
#   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /api/v1/preauthorization/
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/document/
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/benefit/
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/visit/
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/migration/
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
          - path: /api/v1/claim/
            pathType: Prefix
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
  ingressClassName: nginx

---
#################################################################################
####################### 1st Ingress Rule - rewrite ##############################
#################################################################################

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-membership
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
#    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /api/v1/membership/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/provider.*
            pathType: ImplementationSpecific
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/provider/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/device/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/country/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/catalog/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/audit/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
          - path: /api/v1/card/
            pathType: Prefix
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-gateway
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
#    cert-manager.io/cluster-issuer: letsencrypt-prod
# annotations:
#   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /api/v1/search/
            pathType: Prefix
            backend:
              service:
                name: gateway-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-notification
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
#    cert-manager.io/cluster-issuer: letsencrypt-prod
# annotations:
#   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /api/v1/notification/
            pathType: Prefix
            backend:
              service:
                name: notification-service
                port:
                  number: 8080

          - path: /api/v1/events/
            pathType: Prefix
            backend:
              service:
                name: notification-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-documents
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 100m
#    cert-manager.io/cluster-issuer: letsencrypt-prod
# annotations:
#   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /api/file/
            pathType: Prefix
            backend:
              service:
                name: documents-service
                port:
                  number: 8095

          - path: /api/britam/
            pathType: Prefix
            backend:
              service:
                name: documents-service
                port:
                  number: 8095
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-profile
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
#    cert-manager.io/cluster-issuer: letsencrypt-prod
# annotations:
#   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /api/v1/profile/
            pathType: Prefix
            backend:
              service:
                name: profile-service
                port:
                  number: 8080
          - path: /api/v1/biometric/
            pathType: Prefix
            backend:
              service:
                name: profile-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-staging
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    #    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
#    cert-manager.io/cluster-issuer: letsencrypt-prod
# annotations:
#   cert-manager.io/issuer: letsencrypt-nginx
spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /api/v1/staging/
            pathType: Prefix
            backend:
              service:
                name: staging-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-keycloak
  namespace: backend
  annotations:
    kubernetes.io/ingress.class: nginx
    #    cert-manager:io/cluster-issuer: lets-encrypt-staging
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: ALLOWALL";
#      more_set_headers  "Content-Security-Policy "default-src *  data: blob: filesystem: about: ws: wss: 'unsafe-inline' 'unsafe-eval' 'unsafe-dynamic'; script-src * data: blob: 'unsafe-inline' 'unsafe-eval'; connect-src * data: blob: 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src * data: blob: ; style-src * data: blob: 'unsafe-inline'; font-src * data: blob: 'unsafe-inline';"
#      more_set_headers "X-Xss-Protection: 1; mode=block";
#      more_set_headers "X-Content-Type-Options: nosniff";
#      more_clear_headers "Cache-Control";
#      more_set_headers "Cache-Control: must-revalidate";
#      proxy_hide_header Content-Security-Policy;
#      proxy_hide_header "X-Frame-Options";
#      proxy_hide_header "X-Frame-Options";
#      proxy_set_header l5d-dst-override;

#    cert-manager.io/acme-challenge-type: http01
#    nginx.ingress.kubernetes.io/use-regex: "true"
#    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
#    kubernetes.io/ingress.class: nginx
#    cert-manager.io/issuer: letsencrypt-nginx
#  annotations:
#    kubernetes.io/ingress.class: nginx
#    cert-manager:io/cluster-issuer: nlets-encrypt-staging
spec:
  #  tls:
  #    - hosts:
  #        - "*.lctafrica.net"
  #      secretName: lct-tls-secret
  rules:
    - host: keycloak-dev.lctafrica.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: keycloak-service
                port:
                  number: 8090
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-claims-with-rewrite
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
#    cert-manager.io/cluster-issuer: letsencrypt-prod

spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /claims(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: claims-service
                port:
                  number: 8088
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-membership-with-rewrite
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
#    cert-manager.io/cluster-issuer: letsencrypt-prod

spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /membership(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: membership-service
                port:
                  number: 8070
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-documents-with-rewrite
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
#    cert-manager.io/cluster-issuer: letsencrypt-prod

spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /documents(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: documents-service
                port:
                  number: 8095
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-notification-with-rewrite
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
#    cert-manager.io/cluster-issuer: letsencrypt-prod

spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /notification(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: notification-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-profile-with-rewrite
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
#    cert-manager.io/cluster-issuer: letsencrypt-prod

spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /profile(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: profile-service
                port:
                  number: 8080
  ingressClassName: nginx

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-staging
  namespace: backend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
#    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  #  tls:
  #    - hosts:
  #        - "api-uat.lctafrica.net"
  #      secretName: "api-uat-letsencrypt-prod"
  rules:
    - host: "api-dev.lctafrica.net"
      http:
        paths:
          - path: /api/v1/staging/
            pathType: Prefix
            backend:
              service:
                name: staging-service
                port:
                  number: 8080
  ingressClassName: nginx
