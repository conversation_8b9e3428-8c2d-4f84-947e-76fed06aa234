apiVersion: v1
kind: Service
metadata:
  name: keycloak-service
  namespace: backend
  labels:
    app: keycloak
spec:
  ports:
    - name: http
      port: 8090
      targetPort: 8080
  selector:
    app: keycloak
  type: ClusterIP
  sessionAffinity: ClientIP

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: keycloak-deployment
  namespace: backend
  labels:
    app: keycloak
spec:
  replicas: 1
  selector:
    matchLabels:
      app: keycloak
  template:
    metadata:
      labels:
        app: keycloak
    spec:
      containers:
        - name: keycloak
          image: quay.io/keycloak/keycloak:15.1.0
          env:
            - name: KEYCLOAK_USER
              valueFrom:
                secretKeyRef:
                  name: keycloak-secrets
                  key: username
            - name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: keycloak-secrets
                  key: password
            - name: DB_ADDR
              valueFrom:
                configMapKeyRef:
                  name: mysql-config
                  key: host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: mysql-config
                  key: port
            - name: DB_DATABASE
              valueFrom:
                configMapKeyRef:
                  name: mysql-config
                  key: keycloakDbName
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: mysql-secrets
                  key: username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mysql-secrets
                  key: password
            - name: DB_VENDOR
              value: MYSQL
            - name: PROXY_ADDRESS_FORWARDING
              value: 'true'

          ports:
            - name: jgroups
              containerPort: 7600
            - name: http
              containerPort: 8080

          readinessProbe:
            httpGet:
              path: /auth/realms/master
              port: 8080
