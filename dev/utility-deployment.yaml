#apiVersion: v1
#kind: Pod
#metadata:
#  name: utility-pod
#spec:
#  containers:
#    - name: utility-pod
#      image: arunvelsriram/utils
#
apiVersion: apps/v1
kind: Deployment
metadata:
  name: utilities
  namespace: backend
  labels:
    app: utilities
spec:
  replicas: 1
  selector:
    matchLabels:
      app: utilities
  template:
    metadata:
      labels:
        app: utilities
    spec:
      containers:
        - name: utilities
          image: arunvelsriram/utils
          # image: quay.io/sudermanjr/utilities:latest
          # Just spin & wait forever
          command: [ "/bin/bash", "-c", "--" ]
          args: [ "while true; do sleep 30; done;" ]
          securityContext:
             runAsUser: 0
#            readOnlyRootFilesystem: true
#            allowPrivilegeEscalation: false
#            runAsNonRoot: true
#            runAsUser: 10324
#            capabilities:
#              drop:
#                - ALL
          resources:
            requests:
              cpu: 30m
              memory: 64Mi
            limits:
              cpu: 100m
              memory: 128Mi